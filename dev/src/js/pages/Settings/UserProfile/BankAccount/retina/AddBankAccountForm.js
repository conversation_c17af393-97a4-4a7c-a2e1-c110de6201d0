import React, { useState, useEffect, Fragment } from 'react';
import PropTypes from 'prop-types';
import _ from 'lodash';
import {
    FormHelper,
    FormGroup,
    PageDialog,
    Flex,
    Paper,
    Box,
    InputSwitch,
    FormLabel,
    InputSelect,
    InputText,
    InputNumber,
    DialogClose,
    Button,
    Text,
    ToastContext,
    InputSelectTag,
    Upload,
    Tooltip,
    Paragraph,
    TagStatus,
    Separator,
    Progress,
    Badge,
    ResendOTP,
    Banner, BannerDescription,
} from '@majoo-ui/react';
import { TrashOutline, CircleExclamationOutline, CheckFilled, CircleInfoFilled } from '@majoo-ui/icons';
import { useForm } from 'react-hook-form';

import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { Trans } from 'react-i18next';
import { clearSession } from '~/services/api/session.util';
import { uploadImage } from '~/data/upload';
import { usePartialState } from '~/utils/usePartialState';
import { colors } from '../../../../../stitches.config';
import { catchError } from '../../../../../utils/helper';
import {
    ConfirmAdd,
    ConfirmCancel,
    ConfirmDelete,
    VerificationFailed,
    VerificationInformation,
    AlreadyIntegratedModal,
} from './Modal';
import ImagePreviewModal from '../../../../../components/retina/modal/ImagePreviewModal';
import { userRekeningSendOTP, userRekeningValidateOTP, getAccountInfo } from '../../../../../data/users';
import * as employeeApi from '../../../../../data/employee';

import { STATUS_TYPE, BUKA_TABUNGAN_RAYA, responsivePaper } from '../constants';
import { BANK_ID, VERIFY_STEP, ACCOUNT_TYPE } from './enum';
import ModalAdvance from './ModalAdvance';
import { ConditionalWrapper } from '../../../../../components/wrapper/ConditionalWrapper';
import AuthorizationModal from '../../AccountInfo/components/authorizationModal';
import VerificationModal from '../../AccountInfo/components/verificationModal';

const schema = t =>
    yup
        .object()
        .shape({
            id: yup.string(),
            type: yup.string(),
            outlets: yup
                .string()
                .required(t('f.outlet.required'))
                .typeError(t('f.outlet.required'))
                .transform(value => (value.length === 0 ? '' : JSON.stringify(value))),
            bank_id: yup.number().required(t('f.bankName.required')).typeError(t('f.bankName.required')),
            bank_name: yup.string().required(t('f.bankName.required')).typeError(t('f.bankName.required')),
            account_no: yup.string().required(t('f.accNumber.required')),
            account_holder: yup.string().required(t('f.accOwner.required')),
        })
        .when((values, currentSchema) => {
            if (values.bank_id !== BANK_ID.RAYA) {
                return currentSchema.shape({
                    account_image: yup.string().required(t('f.img.required')),
                    ktp_image: yup.string().required(t('f.ktp.required')),
                });
            }
            return currentSchema;
        });

const AddBankAccountForm = props => {
    const {
        open,
        onOpenChange,
        detail,
        outletOptions,
        bankOptions,
        onSubmit,
        setDetail,
        type,
        openInfoModal,
        selectedBank,
        defaultOutlet,
        isMobile,
        marketplaceOptions,
        ewalletOptions,
        isIntegratedWallet,
        isIntegratedWebstore,
        setIsIntegratedWebstore,
        setIsIntegratedWallet,
        t,
        businessProfile,
        showProgress,
        hideProgress,
    } = props;

    const defaultFormValues = {
        id: detail.id || '',
        type: 'rekening',
        outlets: detail.id_outlets || [defaultOutlet],
        bank_id: detail.bank_id || selectedBank.value,
        bank_name: detail.bank_name || selectedBank.name,
        account_no: detail.account_no || '',
        account_image: detail.account_image || '',
        ktp_image: detail.ktp_image || '',
        account_holder: detail.account_holder || '',
        status: detail.is_active === undefined ? true : detail.is_active,
        is_payroll_account: detail.is_payroll_account === undefined ? true : detail.is_payroll_account,
        otp_token: '',
        register_id: '',
        ewallet_settlement: Object.keys(detail).length > 0 ? detail.ewallet_settlement.map(e => `${e.outlet_id}`) : [],
        toko_online_settlement:
            Object.keys(detail).length > 0 ? detail.toko_online_settlement.map(e => `${e.outlet_id}`) : [],
    };

    const {
        register,
        getValues,
        setValue,
        handleSubmit,
        formState: { errors },
        reset,
        clearErrors,
        setError,
        watch,
    } = useForm({
        resolver: yupResolver(schema(t)),
        defaultValues: defaultFormValues,
    });

    const { addToast } = React.useContext(ToastContext);
    const [outlets, setOutlets] = useState(
        detail.id ? getValues('outlets').map(outlet => String(outlet)) : [defaultOutlet],
    );
    const [bank, setBank] = useState(String(getValues('bank_id')));
    const [openConfirmCancel, setOpenConfirmCancel] = useState(false);
    const [openConfirmAdd, setOpenConfirmAdd] = useState(false);
    const [openConfirmDelete, setOpenConfirmDelete] = useState(false);
    const [previewImage, setPreviewImage] = useState('');
    const [payload, setPayload] = useState({});
    const modalTriggerRef = React.useRef(null);
    const [accountFiles, setAccountFiles] = useState(
        getValues('account_image')
            ? [
                  {
                      name: getValues('account_image'),
                      url: getValues('account_image'),
                  },
              ]
            : [],
    );
    const [ktpFiles, setKtpFiles] = useState(
        getValues('ktp_image')
            ? [
                  {
                      name: getValues('ktp_image'),
                      url: getValues('ktp_image'),
                  },
              ]
            : [],
    );
    const [isUploading, setIsUploading] = useState(false);
    const [progressValue, setProgressValue] = useState(0);
    const [step, setStep] = useState(getValues('id') && String(detail.bank_id) === BANK_ID.RAYA ? 4 : 1);
    const [showIntegratedModal, setShowIntegratedModal] = useState(false);
    const [showFailedModal, setShowFailedModal] = useState(false);
    const [showVerifyInformation, setshowVerifyInformation] = useState(false);
    const [isVerifying, setIsVerifying] = useState(false);
    const [isOTPValid, setIsOTPValid] = useState(true);
    const [invalidCount, setInvalidCount] = useState(0);
    const [uploadState, setUploadState] = usePartialState({
        imageURL: '',
        uploadProgress: 0,
        type: '',
    });

    // Authorization and Verification Modal states
    const [openAuthorizationModal, setOpenAuthorizationModal] = useState(false);
    const [verificationModal, setVerificationModal] = useState({
        open: false,
        type: 'phone1',
        isAuth: false,
    });
    const [userData, setUserData] = useState({
        email: businessProfile.ownerEmail,
        phone: businessProfile.ownerPhone,
    });

    // Fetch user profile data for verification
    const fetchUserData = async () => {
        try {
            const response = await getAccountInfo();
            if (response.status) {
                const { data } = response;
                setUserData(current => ({
                    email: current.email || data.user_email || '',
                    phone: current.phone || data.user_notlp || '',
                }));
            }
        } catch (error) {
            addToast({
                title: t('toast.error', { ns: 'translation' }),
                description: catchError(error),
                variant: 'failed',
            });
        }
    };

    // Request verification code for authorization
    const fetchRequestVerifCode = async (verificationType, onSuccess, onError) => {
        showProgress();
        const verificationPayload = {
            type: 'authorize_rekening_merchant',
            value: getValues('account_no'),
            send_to: verificationType,
        };

        try {
            await employeeApi.requestVerificationCode(verificationPayload);
            if (onSuccess) onSuccess();
            setVerificationModal({
                open: true,
                type: verificationType,
            });
            setOpenAuthorizationModal(false);
        } catch (error) {
            if (onError) onError(error);
            addToast({
                title: t('toast.error', { ns: 'translation' }),
                description: catchError(error),
                variant: 'failed',
            });
        } finally {
            hideProgress();
        }
    };

    // Verify the code entered by user
    const fetchVerifyData = async (code, verificationType, onError) => {
        showProgress();
        try {
            await employeeApi.verifyCode({
                type: 'authorize_rekening_merchant',
                code,
            });

            setVerificationModal({ open: false });
            setOpenConfirmAdd(true);
        } catch (error) {
            if (onError) onError(error);
            addToast({
                title: t('toast.error', { ns: 'translation' }),
                description: catchError(error),
                variant: 'failed',
            });
        } finally {
            hideProgress();
        }
    };

    const upload = async (_files, _type = 'account') => {
        const image = await _files;
        if (image.length === 0) return;
        try {
            setIsUploading(true);

            const formdata = new FormData();
            const mimeType = image[0].type.split('/');
            formdata.append('userfile', image[0], `image.${mimeType[1]}`);

            const response = await uploadImage(formdata, progress =>
                setUploadState({ uploadProgress: progress, type: _type }),
            );
            if (!response.item_image_path) throw Error(response.msg);
            setUploadState({ imageURL: response.item_image_path, type: _type });
            setValue(`${_type}_image`, response.item_image_path);
        } catch (e) {
            addToast({
                title: t('toast.error', { ns: 'translation' }),
                description: catchError(e),
                variant: 'failed',
            });
        } finally {
            setIsUploading(false);
        }
    };

    const handlePhotoChange = async (value, _type) => {
        const { fileList, isReplace } = await value;
        upload(fileList, _type);
        if (_type === 'account') {
            if (isReplace) {
                setAccountFiles([]);
            } else {
                setAccountFiles(fileList);
            }
            clearErrors('account_image');
        }

        if (_type === 'ktp') {
            if (isReplace) {
                setKtpFiles([]);
            } else {
                setKtpFiles(fileList);
            }
            clearErrors('ktp_image');
        }
    };

    const handlePhotoPreview = file => {
        modalTriggerRef.current.click();
        setPreviewImage(file.url);
    };

    const handlePhotoRemove = _type => {
        if (_type === 'account') {
            setAccountFiles([]);
            setValue('account_image', '');
        }

        if (_type === 'ktp') {
            setKtpFiles([]);
            setValue('ktp_image', '');
        }
    };

    const handleCancelConfirm = () => {
        setDetail({});
        reset();
        setOpenConfirmCancel(false);
        onOpenChange(false);
        setIsIntegratedWebstore(false);
        setIsIntegratedWallet(false);
    };

    const handleConfirm = () => {
        onSubmit({ payload, type: detail.status_id });
        setOpenConfirmAdd(false);
        reset();
        setDetail({});
        setIsIntegratedWebstore(false);
        setIsIntegratedWallet(false);
    };

    const handleDelete = () => {
        onSubmit({ payload: { id: detail.id }, type: 'DELETE' });
        setOpenConfirmDelete(false);
        reset();
        setDetail({});
        setIsIntegratedWebstore(false);
        setIsIntegratedWallet(false);
    };

    const _onSubmit = _payload => {
        setPayload(_payload);

        // Check if user data is available, if not fetch it first
        if (!userData.email || !userData.phone) {
            fetchUserData().then(() => {
                setOpenAuthorizationModal(true);
            });
        } else {
            setOpenAuthorizationModal(true);
        }
    };

    const isDraftChanged = !_.isEqual(defaultFormValues, watch());

    const checkDisabled = () =>
        (String(detail.status_id) === STATUS_TYPE.ON_PROCESS && String(detail.bank_id) === BANK_ID.RAYA) ||
        String(detail.status_id) === STATUS_TYPE.APPROVED ||
        String(detail.status_id) === STATUS_TYPE.INTEGRATED ||
        step !== VERIFY_STEP.INITIAL;

    const checkDisabledOutlet = () =>
        (String(detail.status_id) === STATUS_TYPE.ON_PROCESS && String(detail.bank_id) === BANK_ID.RAYA) ||
        String(detail.status_id) === STATUS_TYPE.INTEGRATED ||
        step !== VERIFY_STEP.INITIAL;

    const checkDisabledVerifyButton = () =>
        !watch('outlets') ||
        !watch('outlets').length ||
        !watch('account_holder') ||
        errors.account_no ||
        errors.nik ||
        errors.ponsel;

    const checkDisabledSaveButton = () =>
        bank === BANK_ID.RAYA && type === ACCOUNT_TYPE.EXISTING
            ? isUploading || step !== VERIFY_STEP.VERIFIED
            : isUploading || !isDraftChanged;

    const sendVerificationCode = async () => {
        if (!getValues('account_no') || !getValues('nik') || !getValues('ponsel')) {
            if (!getValues('account_no'))
                setError('account_no', { type: 'required', message: t('f.accNumber.required') });
            if (!getValues('nik')) setError('nik', { type: 'required', message: t('errors.ktp') });
            if (!getValues('ponsel')) setError('ponsel', { type: 'required', message: t('errors.phone') });
            return;
        }

        setStep(VERIFY_STEP.PROGRESS);
        setProgressValue(20);
        clearErrors(['account_no', 'nik', 'ponsel']);

        const sendOtpPayload = {
            account_no: getValues('account_no'),
            phone_number: getValues('ponsel'),
            nik: getValues('nik'),
        };

        try {
            const { status, data } = await userRekeningSendOTP(sendOtpPayload);
            if (status) {
                setStep(VERIFY_STEP.VERIFYING);
                setValue('otp_token', data.token);
                if (data.register_id) setValue('register_id', data.register_id);
            }
        } catch (err) {
            setStep(VERIFY_STEP.INITIAL);

            const errMsg = catchError(err);

            if (errMsg.includes('account already integrated')) {
                setShowIntegratedModal(true);
            } else {
                setShowFailedModal(true);
            }

            if (errMsg.includes('Rekening tidak sesuai'))
                setError('account_no', { type: 'validate', message: t('errors.invalidNumber') });
            if (errMsg.includes('Nik tidak valid'))
                setError('nik', { type: 'validate', message: t('errors.invalidKtp') });
            if (errMsg.includes('phone_number') || errMsg.includes('mobileNo'))
                setError('ponsel', { type: 'validate', message: t('errors.invalidPhone') });
        } finally {
            setInvalidCount(0);
            setIsOTPValid(true);
        }
    };

    const ValidateVerificationCode = async () => {
        setIsVerifying(true);
        const validateOtpPayload = {
            account_no: getValues('account_no'),
            phone_number: getValues('ponsel'),
            token: getValues('otp_token'),
            otp: getValues('otp'),
            register_id: getValues('register_id'),
        };

        try {
            const res = await userRekeningValidateOTP(validateOtpPayload);
            if (res.status) {
                setIsOTPValid(true);
                setStep(VERIFY_STEP.VERIFIED);
            }
        } catch (err) {
            setIsOTPValid(false);
            setInvalidCount(prev => prev + 1);
        } finally {
            setIsVerifying(false);
        }
    };

    useEffect(() => {
        if (invalidCount > 2) {
            setshowVerifyInformation(true);
            setStep(VERIFY_STEP.INITIAL);
            setValue('otp', '');
        }
    }, [invalidCount]);

    useEffect(() => {
        if (step === VERIFY_STEP.PROGRESS && progressValue < 100) {
            setTimeout(() => {
                setProgressValue(val => val + 20);
            }, 50);
        }
    }, [step, progressValue]);

    useEffect(() => {
        const { imageURL, uploadProgress, type: _type } = uploadState;
        if (uploadProgress && typeof uploadProgress.percent === 'number' && uploadProgress.percent > 0) {
            if (accountFiles.length > 0 && _type === 'account') {
                setAccountFiles([
                    { ...accountFiles, status: 'uploading', percent: Math.round(uploadProgress.percent) },
                ]);
            }

            if (ktpFiles.length > 0 && _type === 'ktp') {
                setKtpFiles([{ ...ktpFiles, status: 'uploading', percent: Math.round(uploadProgress.percent) }]);
            }
        }
        if (imageURL) {
            if (_type === 'account') {
                setAccountFiles([{ url: imageURL }]);
            }
            if (_type === 'ktp') {
                setKtpFiles([{ url: imageURL }]);
            }
            setUploadState({ uploadProgress: 0, imageURL: '' });
        }
    }, [uploadState]);

    const renderInformation = () => {
        switch (String(detail.status_id)) {
            case STATUS_TYPE.REJECTED:
                return (
                    <Box>
                        <TagStatus type="error">{t('status.rejected')}</TagStatus>
                        <Box css={{ marginTop: '16px' }}>
                            <Paragraph paragraph="longContentRegular" color="primary">
                                {t('info.rejected')}
                            </Paragraph>
                        </Box>
                    </Box>
                );
            case STATUS_TYPE.APPROVED:
                return (
                    <Box>
                        <TagStatus type="success">{t('status.verified')}</TagStatus>
                        <Box css={{ marginTop: '16px' }}>
                            <Paragraph paragraph="longContentRegular" color="primary">
                                {t('info.verified')}
                            </Paragraph>
                        </Box>
                    </Box>
                );
            case STATUS_TYPE.ON_PROCESS:
                return (
                    <Box>
                        <TagStatus type="new" css={{ maxWidth: 'unset' }}>
                            {t('status.pending')}
                        </TagStatus>
                        <Box css={{ marginTop: '16px' }}>
                            <Paragraph paragraph="longContentRegular" color="primary">
                                {t('info.pending')}
                            </Paragraph>
                        </Box>
                    </Box>
                );
            case STATUS_TYPE.INTEGRATED:
                return (
                    <Box>
                        <TagStatus type="success">{t('status.connected')}</TagStatus>
                        <Box css={{ marginTop: '16px' }}>
                            <Paragraph paragraph="longContentRegular" color="primary">
                                {t('info.connected')}
                            </Paragraph>
                        </Box>
                    </Box>
                );
            default:
                return <Box />;
        }
    };

    const renderVerifyBankRaya = () => {
        switch (step) {
            case VERIFY_STEP.INITIAL:
                return (
                    <React.Fragment>
                        <Text color="primary">{t('f.rayaDesc')}</Text>
                        <FormHelper error>{t('f.rayaHelper')}</FormHelper>
                        <FormGroup
                            responsive="input"
                            css={{
                                display: 'flex',
                                flexDirection: 'column',
                                marginTop: '8px',
                            }}
                        >
                            <FormLabel htmlFor="nik" variant="required">
                                {t('f.nik.name')}
                            </FormLabel>
                            <InputNumber
                                {...register('nik')}
                                id="nik"
                                maxLength={16}
                                allowEmptyFormatting
                                isInvalid={!!errors.nik}
                                thousandSeparator={false}
                                placeholder={t('f.nik.placeholder')}
                                disabled={checkDisabled()}
                                onValueChange={({ value }) => {
                                    setValue('nik', value);
                                    clearErrors('nik');
                                }}
                            />
                            {errors.nik && <FormHelper error>{errors.nik.message}</FormHelper>}
                        </FormGroup>
                        <FormGroup
                            responsive="input"
                            css={{
                                display: 'flex',
                                flexDirection: 'column',
                                margin: '8px 0',
                            }}
                        >
                            <FormLabel htmlFor="ponsel" variant="required">
                                {t('f.phone.name')}
                            </FormLabel>
                            <InputNumber
                                {...register('ponsel')}
                                id="ponsel"
                                maxLength={14}
                                allowLeadingZeros
                                allowEmptyFormatting
                                isInvalid={!!errors.ponsel}
                                thousandSeparator={false}
                                placeholder={t('f.phone.placeholder')}
                                disabled={checkDisabled()}
                                onValueChange={({ value }) => {
                                    setValue('ponsel', value);
                                    clearErrors('ponsel');
                                }}
                            />
                            {errors.ponsel && <FormHelper error>{errors.ponsel.message}</FormHelper>}
                        </FormGroup>
                        <Flex justify="start">
                            <Button
                                size="md"
                                type="button"
                                css={{ width: '$full', '@md': { width: 'fit-content' } }}
                                onClick={sendVerificationCode}
                                disabled={checkDisabledVerifyButton()}
                            >
                                {t('f.phone.verify')}
                            </Button>
                        </Flex>
                    </React.Fragment>
                );
            case VERIFY_STEP.PROGRESS:
                return (
                    <Flex direction="column" align="center" gap={2}>
                        <Progress max={100} width="100%" value={progressValue} />
                        <Text>{t('f.phone.loading')}</Text>
                    </Flex>
                );
            case VERIFY_STEP.VERIFYING:
                return (
                    <React.Fragment>
                        <Text>
                            {t('f.phone.inputOTP')} <Text variant="contentButton">{watch('ponsel')}</Text>
                        </Text>
                        <FormGroup
                            responsive="input"
                            css={{
                                display: 'flex',
                                flexDirection: 'column',
                                marginTop: '8px',
                            }}
                        >
                            <FormLabel htmlFor="otp">{t('f.phone.code')}</FormLabel>
                            <InputText
                                id="otp"
                                maxLength={6}
                                {...register('otp')}
                                placeholder={t('f.phone.placeholderOTP')}
                                isInvalid={!isOTPValid}
                                disabled={isVerifying}
                            />
                            {!isOTPValid && <FormHelper error>{t('f.phone.invalidOTP')}</FormHelper>}
                            <Button
                                size="md"
                                type="button"
                                css={{
                                    marginTop: '8px',
                                    width: 'fit-content',
                                }}
                                onClick={ValidateVerificationCode}
                                disabled={!watch('otp') || String(watch('otp')).length < 6 || isVerifying}
                            >
                                {t('f.phone.verifyNow')}
                            </Button>
                            <Flex gap={2} align="center">
                                <Text>{t('f.phone.notReceived')}</Text>
                                <ResendOTP
                                    maxTime={30}
                                    onResendClick={sendVerificationCode}
                                    renderTime={time =>
                                        time > 0 ? (
                                            <Text color="green" variant="status">
                                                00:
                                                {time > 9 ? time : `0${time}`}
                                            </Text>
                                        ) : null
                                    }
                                    renderButton={btnProps =>
                                        btnProps.remainingTime < 1 ? (
                                            <Button
                                                size="xs"
                                                color="green"
                                                buttonType="ghost"
                                                style={{
                                                    fontSize: '12px',
                                                    paddingLeft: '8px',
                                                    paddingRight: '8px',
                                                }}
                                                onClick={btnProps.onClick}
                                                disabled={isVerifying}
                                            >
                                                {t('f.phone.resendOTP')}
                                            </Button>
                                        ) : null
                                    }
                                />
                            </Flex>
                        </FormGroup>
                    </React.Fragment>
                );
            case VERIFY_STEP.VERIFIED:
                return (
                    <Badge
                        variant="label"
                        color="primary"
                        colorScheme="green"
                        badgeVariant="subtle"
                        css={{ width: '100%', padding: '8px' }}
                    >
                        <Flex gap={3} align="center" justify="start" css={{ width: '100%' }}>
                            <CheckFilled color={colors.textGreen} /> {t('f.phone.success')}
                        </Flex>
                    </Badge>
                );
            default:
                return null;
        }
    };

    const renderTitle = () => {
        if (!getValues('id')) {
            if (type === ACCOUNT_TYPE.NEW) {
                return t('f.title.new');
            }
            return t('f.title.add');
        }
        return t('f.title.edit');
    };

    return (
        <React.Fragment>
            <PageDialog
                open={open}
                onOpenChange={val => {
                    if (type === ACCOUNT_TYPE.NEW) {
                        openInfoModal();
                    } else if (!val) {
                        setOpenConfirmCancel(true);
                    }
                }}
            >
                <PageDialog.Title>{renderTitle()}</PageDialog.Title>
                <PageDialog.Content
                    css={{
                        '& .c-hNYnyM': { width: type !== ACCOUNT_TYPE.EXISTING && '$full' },
                        display: 'flex',
                        padding: '16px',
                        '@md': { padding: 'unset', justifyContent: 'center' },
                    }}
                >
                    {type === ACCOUNT_TYPE.EXISTING ? (
                        <Box
                            css={{
                                display: 'flex',
                                flexDirection: 'column',
                                height: 'fit-content',
                                paddingBottom: '40px',
                                margin: '$spacing-03 0',
                                boxShadow: 'none',
                                gap: '$compact',
                                width: '100%',
                                '@md': {
                                    margin: '40px 0',
                                    gap: '$cozy',
                                    maxWidth: 982,
                                },
                            }}
                        >
                            {detail.id && (
                                <ConditionalWrapper
                                    condition={isMobile}
                                    wrapper={c => (
                                        <Box css={{ '& > div': { boxShadow: 'unset', border: '1px solid $bgBorder' } }}>
                                            {c}
                                        </Box>
                                    )}
                                >
                                    <Paper
                                        css={{
                                            padding: '$cozy',
                                            display: 'grid',
                                            gap: '$compact',
                                            height: 'fit-content',
                                        }}
                                    >
                                        {renderInformation()}
                                    </Paper>
                                </ConditionalWrapper>
                            )}
                            <Paper responsive css={responsivePaper}>
                                <FormGroup responsive="input" css={{ display: 'grid !important' }}>
                                    <FormLabel htmlFor="outlets" variant="required">
                                        {t('f.outlet.name')}
                                    </FormLabel>
                                    <InputSelectTag
                                        placeholder={t('placeholder.select', { ns: 'translation' })}
                                        id="outlets"
                                        option={outletOptions}
                                        value={outlets}
                                        onChange={val => {
                                            setOutlets(val);
                                            setValue('outlets', val);
                                            if (errors.outlets && val.length > 0) {
                                                clearErrors('outlets');
                                            }
                                        }}
                                        isInvalid={!!errors.outlets}
                                        readOnly={checkDisabledOutlet()}
                                        labelCounter={t('f.outlet.counter')}
                                    />
                                    {errors.outlets && <FormHelper error>{errors.outlets.message}</FormHelper>}
                                </FormGroup>
                                <FormGroup responsive="input" css={{ display: 'grid !important' }}>
                                    <FormLabel htmlFor="bank_id" variant="required">
                                        {t('f.bankName.name')}
                                    </FormLabel>
                                    <InputSelect
                                        placeholder={t('placeholder.select', { ns: 'translation' })}
                                        id="bank_id"
                                        option={bankOptions}
                                        value={bankOptions.find(val => val.value === bank)}
                                        onChange={({ value, name }) => {
                                            setBank(value);
                                            setValue('bank_id', value);
                                            clearErrors(['bank_id', 'bank_name']);
                                            if (value === BANK_ID.RAYA) {
                                                clearErrors('account_image');
                                                clearErrors('ktp_image');
                                            }
                                            if (value !== BANK_ID.LAINNYA) setValue('bank_name', name);
                                        }}
                                        isInvalid={!!errors.bank_id}
                                        readOnly={checkDisabled()}
                                        search
                                    />
                                    {bank === BANK_ID.LAINNYA && (
                                        <InputText
                                            {...register('bank_name')}
                                            placeholder={t('f.bankName.placeholder')}
                                            isInvalid={!!errors.bank_name}
                                            disabled={checkDisabled()}
                                        />
                                    )}
                                    {(errors.bank_id || errors.bank_name) && (
                                        <FormHelper error>{errors.bank_name.message}</FormHelper>
                                    )}
                                </FormGroup>
                                <FormGroup responsive="input" css={{ display: 'grid !important' }}>
                                    <FormLabel htmlFor="account_no" variant="required">
                                        {t('f.accNumber.name')}
                                    </FormLabel>
                                    <InputText
                                        {...register('account_no', {
                                            onChange: () => clearErrors('account_no'),
                                        })}
                                        type="number"
                                        id="account_no"
                                        placeholder={t('f.accNumber.placeholder')}
                                        isInvalid={!!errors.account_no}
                                        readOnly={checkDisabled()}
                                    />
                                    {errors.account_no && <FormHelper error>{errors.account_no.message}</FormHelper>}
                                </FormGroup>
                                <FormGroup responsive="input" css={{ display: 'grid !important' }}>
                                    <FormLabel htmlFor="account_holder" variant="required">
                                        {t('f.accOwner.name')}
                                    </FormLabel>
                                    <InputText
                                        {...register('account_holder')}
                                        id="account_holder"
                                        placeholder={t('f.accOwner.placeholder')}
                                        readOnly={checkDisabled()}
                                        isInvalid={!!errors.account_holder}
                                    />
                                    {errors.account_holder && (
                                        <FormHelper error>{errors.account_holder.message}</FormHelper>
                                    )}
                                </FormGroup>
                                {bank !== BANK_ID.RAYA && (
                                    <React.Fragment>
                                        <FormGroup responsive="input" css={{ display: 'grid !important' }}>
                                            <Flex css={{ '@sm': { gap: '$compact' }, '@md': { gap: 'unset' } }}>
                                                <FormGroup
                                                    responsive="input"
                                                    style={{ width: isMobile ? 'max-content' : '144px', columnGap: 0 }}
                                                >
                                                    <FormLabel
                                                        style={{ width: isMobile ? 'max-content' : '144px' }}
                                                        htmlFor="account_image"
                                                        variant="required"
                                                    >
                                                        {t('f.img.name')}
                                                    </FormLabel>
                                                </FormGroup>
                                                <span style={{ position: 'relative', bottom: isMobile ? 0 : '-17px' }}>
                                                    <Tooltip
                                                        withClick={isMobile}
                                                        css={{
                                                            maxWidth: window.innerWidth - 16,
                                                            '@md': { maxWidth: 250 },
                                                        }}
                                                        align="start"
                                                        side="top"
                                                        label={<Trans t={t} i18nKey="f.img.tooltip" />}
                                                    >
                                                        <CircleExclamationOutline
                                                            size={16}
                                                            color={colors.iconSecondary}
                                                        />
                                                    </Tooltip>
                                                </span>
                                            </Flex>
                                            <Box
                                                css={{
                                                    width: 300,
                                                    '& svg[type="close"]': { display: checkDisabled() && 'none' },
                                                    '& button[buttontype="ghost"]': {
                                                        display: checkDisabled() && 'none',
                                                    },
                                                    '& img': { height: checkDisabled() && 140 },
                                                }}
                                            >
                                                <Upload
                                                    fileList={accountFiles}
                                                    onError={(error, response) => {
                                                        if (error) {
                                                            addToast({
                                                                title: t('toast.error', { ns: 'translation' }),
                                                                description: catchError(response),
                                                                variant: 'failed',
                                                            });
                                                            if (error.message === 'Unauthorized') {
                                                                setTimeout(() => {
                                                                    clearSession();
                                                                }, 1000);
                                                            }
                                                        }
                                                    }}
                                                    max={1}
                                                    multiple={false}
                                                    listType="picture"
                                                    width={300}
                                                    errorMessage={errors.account_image ? ' ' : undefined}
                                                    onPreview={handlePhotoPreview}
                                                    onChange={e => handlePhotoChange(e, 'account')}
                                                    onRemove={() => handlePhotoRemove('account')}
                                                    showCropper
                                                    disabled={checkDisabled()}
                                                    accept="image/*"
                                                />
                                                <Flex justify="center">
                                                    {errors.account_image && (
                                                        <FormHelper
                                                            css={{ textAlign: 'center !important', width: 225 }}
                                                            error
                                                        >
                                                            {errors.account_image.message}
                                                        </FormHelper>
                                                    )}
                                                </Flex>
                                            </Box>
                                        </FormGroup>
                                        <FormGroup responsive="input" css={{ display: 'grid !important' }}>
                                            <Flex css={{ '@sm': { gap: '$compact' }, '@md': { gap: 'unset' } }}>
                                                <FormGroup
                                                    responsive="input"
                                                    style={{ width: isMobile ? 'max-content' : '144px', columnGap: 0 }}
                                                >
                                                    <FormLabel
                                                        style={{ width: isMobile ? 'max-content' : '144px' }}
                                                        htmlFor="ktp_image"
                                                        variant="required"
                                                    >
                                                        {t('f.ktp.name')}
                                                    </FormLabel>
                                                </FormGroup>
                                            </Flex>
                                            <Box
                                                css={{
                                                    width: 300,
                                                    '& svg[type="close"]': { display: checkDisabled() && 'none' },
                                                    '& button[buttontype="ghost"]': {
                                                        display: checkDisabled() && 'none',
                                                    },
                                                    '& img': { height: checkDisabled() && 140 },
                                                }}
                                            >
                                                <Upload
                                                    fileList={ktpFiles}
                                                    onError={(error, response) => {
                                                        if (error) {
                                                            addToast({
                                                                title: t('toast.error', { ns: 'translation' }),
                                                                description: catchError(response),
                                                                variant: 'failed',
                                                            });
                                                            if (error.message === 'Unauthorized') {
                                                                setTimeout(() => {
                                                                    clearSession();
                                                                }, 1000);
                                                            }
                                                        }
                                                    }}
                                                    max={1}
                                                    multiple={false}
                                                    listType="picture"
                                                    width={300}
                                                    errorMessage={errors.ktp_image ? ' ' : undefined}
                                                    onPreview={handlePhotoPreview}
                                                    onChange={e => handlePhotoChange(e, 'ktp')}
                                                    onRemove={() => handlePhotoRemove('ktp')}
                                                    showCropper
                                                    disabled={checkDisabled()}
                                                    accept="image/*"
                                                />
                                                <Flex justify="center">
                                                    {errors.ktp_image && (
                                                        <FormHelper
                                                            css={{ textAlign: 'center !important', width: 225 }}
                                                            error
                                                        >
                                                            {errors.ktp_image.message}
                                                        </FormHelper>
                                                    )}
                                                </Flex>
                                            </Box>
                                        </FormGroup>
                                    </React.Fragment>
                                )}
                                {String(watch('bank_id')) === BANK_ID.RAYA && (
                                    <React.Fragment>
                                        <Separator />
                                        {String(detail.status_id) !== STATUS_TYPE.INTEGRATED && (
                                            <FormGroup responsive="input" css={{ display: 'grid !important' }}>
                                                <FormLabel>{t('f.verify')}</FormLabel>
                                                {renderVerifyBankRaya()}
                                            </FormGroup>
                                        )}
                                        {bank === BANK_ID.RAYA && step === VERIFY_STEP.VERIFIED && (
                                            <React.Fragment>
                                                <FormGroup responsive="switch" css={{ display: 'grid !important' }}>
                                                    <FormLabel>{t('f.activate')}</FormLabel>
                                                    <InputSwitch
                                                        dataOnLabel="ON"
                                                        dataOffLabel="OFF"
                                                        defaultChecked={!!getValues('status')}
                                                        onCheckedChange={val => setValue('status', val)}
                                                    />
                                                </FormGroup>
                                                <FormGroup responsive="switch" css={{ display: 'grid !important' }}>
                                                    <FormLabel>{t('f.forPayment')}</FormLabel>
                                                    <InputSwitch
                                                        dataOnLabel="ON"
                                                        dataOffLabel="OFF"
                                                        defaultChecked={!!getValues('is_payroll_account')}
                                                        onCheckedChange={val => setValue('is_payroll_account', val)}
                                                    />
                                                </FormGroup>
                                            </React.Fragment>
                                        )}
                                    </React.Fragment>
                                )}
                                {String(detail.status_id) === STATUS_TYPE.APPROVED &&
                                    String(detail.bank_id) !== BANK_ID.RAYA && (
                                        <FormGroup responsive="switch" css={{ display: 'grid !important' }}>
                                            <FormLabel>Status</FormLabel>
                                            <Box>
                                                <InputSwitch
                                                    dataOffLabel="OFF"
                                                    dataOnLabel="ON"
                                                    defaultChecked={!!getValues('status')}
                                                    onCheckedChange={val => setValue('status', val)}
                                                />
                                            </Box>
                                            <Text variant="helper" color="secondary">
                                                {t('f.activeLabel')}
                                            </Text>
                                        </FormGroup>
                                    )}
                            </Paper>
                            {(String(detail.status_id) === STATUS_TYPE.APPROVED ||
                                String(detail.status_id) === STATUS_TYPE.INTEGRATED) && (
                                <Fragment>
                                    {isIntegratedWallet && (
                                        <Paper responsive css={responsivePaper}>
                                            <Text variant="contentButton" as="h2">
                                                {t('settlement.form.settlementEwallet')}
                                            </Text>
                                            <Text variant="helper" color="secondary">
                                                {t('settlement.form.settlementEwalletDesc')}
                                            </Text>
                                            <FormGroup
                                                responsive="input"
                                                css={{ display: 'grid !important', alignItems: 'center' }}
                                            >
                                                <FormLabel>{t('settlement.outlet')}</FormLabel>
                                                <InputSelectTag
                                                    placeholder={t('placeholder.select', { ns: 'translation' })}
                                                    id="ewallet_settlement"
                                                    option={ewalletOptions}
                                                    value={getValues('ewallet_settlement')}
                                                    showSelectAll={false}
                                                    onChange={value => {
                                                        setValue('ewallet_settlement', value);
                                                        if (errors.ewallet_settlement && value.length > 0) {
                                                            clearErrors('ewallet_settlement');
                                                        }
                                                    }}
                                                    isInvalid={!!errors.ewallet_settlement}
                                                    disabled={String(getValues('id')).length > 0}
                                                />
                                                {String(getValues('id')).length > 0 && (
                                                    <Banner
                                                        variant="info"
                                                        css={{ marginTop: 10 }}
                                                    >
                                                        <Flex gap={3} align="start">
                                                            <CircleInfoFilled />
                                                            <BannerDescription>
                                                                {t('bannerInfo')}
                                                            </BannerDescription>
                                                        </Flex>
                                                    </Banner>
                                                )}
                                            </FormGroup>
                                        </Paper>
                                    )}
                                    {isIntegratedWebstore && (
                                        <Paper responsive css={responsivePaper}>
                                            <Text variant="contentButton" as="h2">
                                                {t('settlement.form.settlementOnlineStore')}
                                            </Text>
                                            <Text variant="helper" color="secondary">
                                                {t('settlement.activateDesc')}
                                            </Text>
                                            <FormGroup responsive="input" css={{ display: 'grid !important' }}>
                                                <FormLabel>{t('settlement.activate')}</FormLabel>
                                                <InputSelectTag
                                                    placeholder={t('placeholder.select', { ns: 'translation' })}
                                                    id="toko_online_settlement"
                                                    option={marketplaceOptions}
                                                    value={getValues('toko_online_settlement')}
                                                    showSelectAll={false}
                                                    onChange={value => {
                                                        setValue('toko_online_settlement', value);
                                                        if (errors.toko_online_settlement && value.length > 0) {
                                                            clearErrors('toko_online_settlement');
                                                        }
                                                    }}
                                                    isInvalid={!!errors.toko_online_settlement}
                                                    disabled={String(getValues('id')).length > 0}
                                                />
                                                {String(getValues('id')).length > 0 && (
                                                    <Banner
                                                        variant="info"
                                                        css={{ marginTop: 10 }}
                                                    >
                                                        <Flex gap={3} align="start">
                                                            <CircleInfoFilled />
                                                            <BannerDescription>
                                                                {t('bannerInfo')}
                                                            </BannerDescription>
                                                        </Flex>
                                                    </Banner>
                                                )}
                                            </FormGroup>
                                        </Paper>
                                    )}
                                </Fragment>
                            )}
                        </Box>
                    ) : (
                        <iframe
                            src={BUKA_TABUNGAN_RAYA}
                            allow={`camera ${BUKA_TABUNGAN_RAYA}`}
                            title={t('f.title.addRaya')}
                            style={{
                                border: 0,
                                width: '100%',
                                height: 'calc(100% - 6px)',
                            }}
                        />
                    )}
                </PageDialog.Content>

                <PageDialog.Footer
                    css={{
                        justifyContent: 'center',
                        height: 'auto',
                        padding: '$spacing-05',
                        borderTop: '1px solid $gray150',
                        '@md': { height: 64, padding: '12px 24px', borderTop: 'none' },
                    }}
                >
                    <Box
                        css={{
                            display: 'flex',
                            width: '100%',
                            gap: '$compact',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            '@md': {
                                maxWidth: 982,
                                display: 'flex',
                            },
                        }}
                    >
                        {String(detail.status_id) === STATUS_TYPE.REJECTED ||
                        String(detail.status_id) === STATUS_TYPE.APPROVED ||
                        String(detail.status_id) === STATUS_TYPE.INTEGRATED ? (
                            <React.Fragment>
                                {isMobile ? (
                                    <ModalAdvance isMobile onDelete={() => setOpenConfirmDelete(true)} />
                                ) : (
                                    <Button
                                        buttonType="negative-secondary"
                                        css={{ minWidth: 'unset', width: 40, padding: 0 }}
                                        onClick={() => setOpenConfirmDelete(true)}
                                    >
                                        <TrashOutline color={colors.iconRed} />
                                    </Button>
                                )}
                            </React.Fragment>
                        ) : (
                            <Box />
                        )}
                        {type === ACCOUNT_TYPE.NEW ? (
                            <Flex justify="between" css={{ width: '$full', '@md': { width: 'unset' } }}>
                                <DialogClose asChild>
                                    <Button css={{ flex: 1, '@md': { flex: 'unset' } }} buttonType="ghost">
                                        {t('label.close', { ns: 'translation' })}
                                    </Button>
                                </DialogClose>
                            </Flex>
                        ) : (
                            <Flex gap={5} css={{ width: '$full', '@md': { width: 'unset' } }}>
                                <DialogClose asChild>
                                    <Button
                                        css={{ width: '50%', '@md': { width: 'unset' } }}
                                        size="md"
                                        type="button"
                                        buttonType="ghost"
                                    >
                                        {t('label.cancel', { ns: 'translation' })}
                                    </Button>
                                </DialogClose>
                                <Button
                                    css={{ width: '50%', '@md': { width: 'unset' } }}
                                    size="md"
                                    type="button"
                                    disabled={checkDisabledSaveButton()}
                                    onClick={type === ACCOUNT_TYPE.NEW ? openInfoModal : handleSubmit(_onSubmit)}
                                >
                                    {type === ACCOUNT_TYPE.NEW
                                        ? t('label.done', { ns: 'translation' })
                                        : t('label.save', { ns: 'translation' })}
                                </Button>
                            </Flex>
                        )}
                    </Box>
                </PageDialog.Footer>
            </PageDialog>
            <ConfirmCancel
                open={openConfirmCancel}
                onOpenChange={setOpenConfirmCancel}
                isEditMode={!!getValues('id')}
                onConfirm={handleCancelConfirm}
                t={t}
            />
            <ConfirmAdd
                open={openConfirmAdd}
                onOpenChange={setOpenConfirmAdd}
                isEditMode={!!getValues('id')}
                onConfirm={handleConfirm}
                t={t}
            />
            {openConfirmDelete && (
                <ConfirmDelete
                    detail={detail}
                    open={openConfirmDelete}
                    onOpenChange={setOpenConfirmDelete}
                    onConfirm={handleDelete}
                    t={t}
                />
            )}
            <ImagePreviewModal ref={modalTriggerRef} previewTitle="" previewImage={previewImage} />
            <VerificationFailed
                open={showFailedModal}
                onOpenChange={setShowFailedModal}
                onConfirm={() => setShowFailedModal(false)}
                t={t}
            />
            <VerificationInformation
                open={showVerifyInformation}
                onOpenChange={setshowVerifyInformation}
                onConfirm={() => setshowVerifyInformation(false)}
                t={t}
            />
            <AlreadyIntegratedModal
                open={showIntegratedModal}
                onOpenChange={setShowIntegratedModal}
                onConfirm={() => setShowIntegratedModal(false)}
                t={t}
            />

            {/* Authorization Modal */}
            {openAuthorizationModal && (
                <AuthorizationModal
                    title={t('titleSection.authorization', 'Otorisasi Rekening Bank')}
                    isOpen={openAuthorizationModal}
                    onOpenChange={setOpenAuthorizationModal}
                    phoneNumber={userData.phone}
                    email={userData.email}
                    requestVerification={verificationType => fetchRequestVerifCode(verificationType)}
                    isMobile={isMobile}
                    t={t}
                />
            )}

            {/* Verification Modal */}
            {verificationModal.open && (
                <VerificationModal
                    isMobile={isMobile}
                    isOpen={verificationModal.open}
                    onOpenChange={isOpen => setVerificationModal(current => ({ ...current, open: isOpen }))}
                    verificationType={verificationModal.type}
                    contactValue={verificationModal.type === 'email' ? userData.email : userData.phone}
                    requestVerificationCode={async (verificationType, _, onSuccess, onError) => {
                        await fetchRequestVerifCode(verificationType, onSuccess, onError);
                    }}
                    onSubmit={(code, verificationType, onError) => fetchVerifyData(code, verificationType, onError)}
                    t={t}
                />
            )}
        </React.Fragment>
    );
};

AddBankAccountForm.propTypes = {
    open: PropTypes.bool.isRequired,
    onOpenChange: PropTypes.func.isRequired,
    detail: PropTypes.shape({
        id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
        id_outlets: PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.string, PropTypes.number])),
        bank_id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
        bank_name: PropTypes.string,
        account_no: PropTypes.string,
        account_image: PropTypes.string,
        ktp_image: PropTypes.string,
        account_holder: PropTypes.string,
        is_active: PropTypes.oneOfType([PropTypes.number, PropTypes.bool]),
        is_payroll_account: PropTypes.oneOfType([PropTypes.number, PropTypes.bool]),
        ewallet_settlement: PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.string, PropTypes.number])),
        toko_online_settlement: PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.string, PropTypes.number])),
        status_id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    }),
    outletOptions: PropTypes.arrayOf(PropTypes.shape({})),
    bankOptions: PropTypes.arrayOf(PropTypes.shape({})),
    onSubmit: PropTypes.func.isRequired,
    setDetail: PropTypes.func.isRequired,
    isMobile: PropTypes.bool,
    t: PropTypes.func.isRequired,
    type: PropTypes.string,
    openInfoModal: PropTypes.func,
    selectedBank: PropTypes.shape({
        value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
        name: PropTypes.string,
    }),
    defaultOutlet: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    marketplaceOptions: PropTypes.arrayOf(PropTypes.shape({})),
    ewalletOptions: PropTypes.arrayOf(PropTypes.shape({})),
    isIntegratedWallet: PropTypes.bool,
    isIntegratedWebstore: PropTypes.bool,
    setIsIntegratedWebstore: PropTypes.func,
    setIsIntegratedWallet: PropTypes.func,
    businessProfile: PropTypes.shape({
        ownerPhone: PropTypes.string,
        ownerEmail: PropTypes.string,
    }),
    showProgress: PropTypes.func,
    hideProgress: PropTypes.func,
};

AddBankAccountForm.defaultProps = {
    isMobile: false,
    detail: {},
    outletOptions: [{}],
    bankOptions: [{}],
    type: 'rekening',
    openInfoModal: () => {},
    selectedBank: {},
    defaultOutlet: '',
    marketplaceOptions: [],
    ewalletOptions: [],
    isIntegratedWallet: false,
    isIntegratedWebstore: false,
    setIsIntegratedWebstore: () => {},
    setIsIntegratedWallet: () => {},
    businessProfile: {
        ownerEmail: '',
        ownerPhone: ''
    },
    showProgress: () => {},
    hideProgress: () => {},
};

export default AddBankAccountForm;
