import React from 'react';
import * as PropTypes from 'prop-types';
import {
    ModalDialog,
    ModalDialogTitle,
    ModalDialogContent,
    Text,
} from '@majoo-ui/react';

const SettlementAccountModal = ({ open, setOpen, isMobile, t } ) => {
    return (
        <ModalDialog size="lg" isMobile={isMobile} open={open} onOpenChange={setOpen}>
            <ModalDialogTitle>Atur Rekening Settlement</ModalDialogTitle>
            <ModalDialogContent></ModalDialogContent>
        </ModalDialog>
    );
};

SettlementAccountModal.propTypes = {
    isMobile: PropTypes.bool,
    t: PropTypes.func,
};

SettlementAccountModal.defaultProps = {
    isMobile: false,
    t: (_, val) => val,
};

export default SettlementAccountModal;
